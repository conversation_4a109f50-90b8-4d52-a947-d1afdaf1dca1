#!/usr/bin/env python3
"""
TikTok Upload Automation
Free browser automation for uploading viral clips to TikTok
"""

import asyncio
import json
import random
import re
from pathlib import Path
from typing import Dict, List, Optional, Tuple
from datetime import datetime

from playwright_base import PlaywrightBase


class TikTokUploader(PlaywrightBase):
    """TikTok upload automation using Playwright."""
    
    def __init__(self, headless: bool = True, debug: bool = False):
        super().__init__("tiktok", headless, debug)
        self.upload_url = "https://www.tiktok.com/upload"
        self.login_url = "https://www.tiktok.com/login"
        
    async def login_check(self) -> bool:
        """Check if already logged in to TikTok."""
        try:
            await self.page.goto(self.upload_url, wait_until="networkidle")
            await self.human_delay(2, 4)
            
            # Check for upload interface elements
            upload_selectors = [
                '[data-e2e="upload-btn"]',
                'input[type="file"]',
                '.upload-btn',
                '[data-testid="upload"]'
            ]
            
            for selector in upload_selectors:
                try:
                    element = await self.page.wait_for_selector(selector, timeout=5000)
                    if element:
                        self.log("Already logged in to TikTok")
                        return True
                except:
                    continue
            
            # Check if redirected to login
            current_url = self.page.url
            if "login" in current_url.lower():
                self.log("Not logged in - redirected to login page")
                return False
            
            self.log("Login status unclear - assuming not logged in")
            return False
            
        except Exception as e:
            self.log(f"Error checking login status: {e}")
            return False
    
    async def manual_login_prompt(self) -> bool:
        """Prompt for manual login and wait for completion."""
        self.log("=== MANUAL LOGIN REQUIRED ===")
        self.log("Please complete the following steps:")
        self.log("1. Open your browser to the TikTok upload page")
        self.log("2. Log in to your TikTok account")
        self.log("3. Navigate to the upload page")
        self.log("4. Return here and press Enter when ready")
        
        try:
            # Navigate to login page
            await self.page.goto(self.login_url, wait_until="networkidle")
            await self.human_delay(2, 3)
            
            if not self.headless:
                # In non-headless mode, wait for user to complete login
                self.log("Browser window opened - please complete login manually")
                
                # Wait for successful login (check for upload page elements)
                max_wait = 300  # 5 minutes
                check_interval = 5
                
                for i in range(0, max_wait, check_interval):
                    await asyncio.sleep(check_interval)
                    
                    if await self.login_check():
                        self.log("Login detected successfully!")
                        await self.save_session()
                        return True
                    
                    if i % 30 == 0:  # Every 30 seconds
                        self.log(f"Still waiting for login... ({max_wait - i}s remaining)")
                
                self.log("Login timeout - please try again")
                return False
            else:
                self.log("ERROR: Manual login requires headless=False mode")
                return False
                
        except Exception as e:
            self.log(f"Error during manual login: {e}")
            return False
    
    async def upload_video(self, video_path: str, title: str, hashtags: List[str] = None) -> Dict:
        """Upload video to TikTok with metadata."""
        try:
            self.log(f"Starting upload: {video_path}")
            
            # Ensure we're on upload page
            await self.page.goto(self.upload_url, wait_until="networkidle")
            await self.human_delay(2, 4)
            
            # Find file input
            file_input_selectors = [
                'input[type="file"]',
                '[data-e2e="upload-btn"] input',
                '.upload-btn input[type="file"]'
            ]
            
            file_input = None
            for selector in file_input_selectors:
                try:
                    file_input = await self.page.wait_for_selector(selector, timeout=5000)
                    if file_input:
                        break
                except:
                    continue
            
            if not file_input:
                raise Exception("Could not find file upload input")
            
            # Upload file
            self.log("Uploading video file...")
            await file_input.set_input_files(video_path)
            await self.human_delay(3, 5)
            
            # Wait for upload to process
            self.log("Waiting for video processing...")
            await self.wait_for_upload_processing()
            
            # Fill in description/caption
            await self.fill_description(title, hashtags)
            
            # Set privacy settings (public)
            await self.set_privacy_settings()
            
            # Publish video
            result = await self.publish_video()
            
            if result["success"]:
                self.log(f"Video uploaded successfully: {result.get('video_url', 'URL not captured')}")
            else:
                self.log(f"Upload failed: {result.get('error', 'Unknown error')}")
            
            return result
            
        except Exception as e:
            error_msg = f"Upload error: {e}"
            self.log(error_msg)
            await self.screenshot(f"upload_error_{datetime.now().strftime('%Y%m%d_%H%M%S')}")
            return {"success": False, "error": error_msg}
    
    async def wait_for_upload_processing(self) -> None:
        """Wait for TikTok to process uploaded video."""
        max_wait = 120  # 2 minutes
        check_interval = 2
        
        processing_indicators = [
            '.upload-progress',
            '[data-e2e="upload-progress"]',
            '.processing',
            '.uploading'
        ]
        
        completion_indicators = [
            '[data-e2e="post-btn"]',
            '.post-btn',
            'button[type="submit"]',
            '[data-testid="post"]'
        ]
        
        self.log("Waiting for video processing to complete...")
        
        for i in range(0, max_wait, check_interval):
            await asyncio.sleep(check_interval)
            
            # Check if processing is complete
            for selector in completion_indicators:
                try:
                    element = await self.page.wait_for_selector(selector, timeout=1000)
                    if element and await element.is_enabled():
                        self.log("Video processing completed")
                        return
                except:
                    continue
            
            # Check if still processing
            still_processing = False
            for selector in processing_indicators:
                try:
                    element = await self.page.query_selector(selector)
                    if element:
                        still_processing = True
                        break
                except:
                    continue
            
            if not still_processing and i > 10:  # Give it at least 10 seconds
                self.log("No processing indicators found - assuming complete")
                return
            
            if i % 20 == 0:  # Every 20 seconds
                self.log(f"Still processing... ({max_wait - i}s remaining)")
        
        self.log("Processing timeout - continuing anyway")
    
    async def fill_description(self, title: str, hashtags: List[str] = None) -> None:
        """Fill in video description and hashtags."""
        try:
            # Common description field selectors
            description_selectors = [
                '[data-e2e="video-caption"]',
                'textarea[placeholder*="caption"]',
                'textarea[placeholder*="description"]',
                '.caption-input textarea',
                '[data-testid="caption"] textarea'
            ]
            
            description_field = None
            for selector in description_selectors:
                try:
                    description_field = await self.page.wait_for_selector(selector, timeout=5000)
                    if description_field:
                        break
                except:
                    continue
            
            if not description_field:
                self.log("Warning: Could not find description field")
                return
            
            # Prepare description text
            description_text = title
            
            if hashtags:
                # Add hashtags
                hashtag_text = " " + " ".join([f"#{tag.strip('#')}" for tag in hashtags])
                description_text += hashtag_text
            
            # Add default viral hashtags if none provided
            if not hashtags:
                default_hashtags = ["#viral", "#fyp", "#trending"]
                description_text += " " + " ".join(default_hashtags)
            
            self.log(f"Filling description: {description_text[:100]}...")
            await self.human_type(description_selectors[0], description_text)
            await self.human_delay(1, 2)
            
        except Exception as e:
            self.log(f"Error filling description: {e}")
    
    async def set_privacy_settings(self) -> None:
        """Set video to public."""
        try:
            # Look for privacy/visibility settings
            public_selectors = [
                '[data-e2e="public-post"]',
                'input[value="public"]',
                '.privacy-public',
                '[data-testid="public"]'
            ]
            
            for selector in public_selectors:
                try:
                    element = await self.page.wait_for_selector(selector, timeout=3000)
                    if element:
                        await self.human_click(selector)
                        self.log("Set video to public")
                        return
                except:
                    continue
            
            self.log("Could not find privacy settings - using default")
            
        except Exception as e:
            self.log(f"Error setting privacy: {e}")
    
    async def publish_video(self) -> Dict:
        """Publish the uploaded video."""
        try:
            # Find publish/post button
            publish_selectors = [
                '[data-e2e="post-btn"]',
                '.post-btn',
                'button[type="submit"]',
                '[data-testid="post"]',
                'button:has-text("Post")',
                'button:has-text("Publish")'
            ]
            
            publish_button = None
            for selector in publish_selectors:
                try:
                    publish_button = await self.page.wait_for_selector(selector, timeout=5000)
                    if publish_button and await publish_button.is_enabled():
                        break
                except:
                    continue
            
            if not publish_button:
                raise Exception("Could not find publish button")
            
            self.log("Publishing video...")
            await self.human_click(publish_selectors[0])
            
            # Wait for publish confirmation
            await self.wait_for_publish_confirmation()
            
            # Try to capture video URL
            video_url = await self.get_published_video_url()
            
            return {
                "success": True,
                "video_url": video_url,
                "published_at": datetime.now().isoformat()
            }
            
        except Exception as e:
            return {"success": False, "error": str(e)}
    
    async def wait_for_publish_confirmation(self) -> None:
        """Wait for publish confirmation."""
        confirmation_selectors = [
            '.success-message',
            '[data-e2e="upload-success"]',
            '.upload-complete',
            'text="Your video is being processed"',
            'text="Video uploaded successfully"'
        ]
        
        max_wait = 30
        for i in range(max_wait):
            await asyncio.sleep(1)
            
            for selector in confirmation_selectors:
                try:
                    element = await self.page.query_selector(selector)
                    if element:
                        self.log("Publish confirmation detected")
                        return
                except:
                    continue
            
            # Check URL change
            if "upload" not in self.page.url.lower():
                self.log("URL changed - assuming publish successful")
                return
        
        self.log("No clear publish confirmation - assuming success")
    
    async def get_published_video_url(self) -> str:
        """Try to capture the published video URL."""
        try:
            await self.human_delay(2, 4)
            
            # Check if redirected to video page
            current_url = self.page.url
            if "tiktok.com/@" in current_url and "/video/" in current_url:
                self.log(f"Captured video URL: {current_url}")
                return current_url
            
            # Look for share/view buttons that might contain URL
            url_selectors = [
                'a[href*="/video/"]',
                '[data-e2e="share-btn"]',
                '.share-link'
            ]
            
            for selector in url_selectors:
                try:
                    element = await self.page.query_selector(selector)
                    if element:
                        href = await element.get_attribute("href")
                        if href and "/video/" in href:
                            return href
                except:
                    continue
            
            return current_url
            
        except Exception as e:
            self.log(f"Could not capture video URL: {e}")
            return ""
    
    def generate_hashtags(self, title: str, max_hashtags: int = 5) -> List[str]:
        """Generate relevant hashtags from title."""
        # Extract keywords from title
        words = re.findall(r'\b\w+\b', title.lower())
        
        # Common viral hashtags
        viral_hashtags = ["viral", "fyp", "trending", "foryou", "tiktok"]
        
        # Content-based hashtags
        content_hashtags = []
        for word in words:
            if len(word) > 3 and word not in ["the", "and", "for", "with", "this", "that"]:
                content_hashtags.append(word)
        
        # Combine and limit
        all_hashtags = viral_hashtags + content_hashtags[:3]
        return all_hashtags[:max_hashtags]
