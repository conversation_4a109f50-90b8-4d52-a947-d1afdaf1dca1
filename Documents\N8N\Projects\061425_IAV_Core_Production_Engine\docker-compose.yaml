version: "3.9"

services:
  postgres:
    image: postgres:15-alpine
    container_name: iav_postgres
    env_file:
      - config.env
    volumes:
      - ./db_data:/var/lib/postgresql/data
    healthcheck:
      test: ["CMD", "pg_isready", "-U", "${POSTGRES_USER:-n8n}"]
      interval: ${HEALTH_CHECK_INTERVAL:-30s}
      timeout: ${HEALTH_CHECK_TIMEOUT:-10s}
      retries: ${HEALTH_CHECK_RETRIES:-5}
    restart: unless-stopped

  redis:
    image: redis:7-alpine
    container_name: iav_redis
    env_file:
      - config.env
    volumes:
      - ./redis_data:/data
    command: ["redis-server", "--save", "60", "1", "--appendonly", "yes"]
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: ${HEALTH_CHECK_INTERVAL:-30s}
      timeout: ${HEALTH_CHECK_TIMEOUT:-10s}
      retries: ${HEALTH_CHECK_RETRIES:-5}
    restart: unless-stopped

  ollama:
    image: ollama/ollama:latest
    container_name: iav_ollama
    env_file:
      - config.env
    deploy:
      resources:
        reservations:
          devices:
            - capabilities: [gpu]
    volumes:
      - ./ollama_data:/root/.ollama
      - ./data:/data
    ports:
      - "${OLLAMA_PORT:-11534}:11434"
    healthcheck:
      test: ["CMD", "ollama", "list"]
      interval: ${HEALTH_CHECK_INTERVAL:-30s}
      timeout: ${HEALTH_CHECK_TIMEOUT:-10s}
      retries: ${HEALTH_CHECK_RETRIES:-5}
    restart: unless-stopped

  n8n:
    build:
      context: .
      dockerfile: Dockerfile.n8n
    container_name: iav_n8n
    user: "${UID}:${GID}"
    env_file:
      - config.env
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
      ollama:
        condition: service_healthy
    ports:
      - "${N8N_PORT:-8678}:${N8N_PORT:-8678}"
    volumes:
      - ./data:/data
      - ./n8n_data:/home/<USER>/.n8n
    deploy:
      resources:
        reservations:
          devices:
            - capabilities: [gpu]
    healthcheck:
      test: ["CMD", "wget", "-qO-", "http://localhost:${N8N_PORT:-8678}/healthz"]
      interval: ${HEALTH_CHECK_INTERVAL:-30s}
      timeout: ${HEALTH_CHECK_TIMEOUT:-10s}
      retries: ${HEALTH_CHECK_RETRIES:-5}
    restart: unless-stopped

  # Whisper ASR service for transcription
  whisper:
    image: onerahmet/openai-whisper-asr-webservice:latest
    container_name: iav_whisper
    env_file:
      - config.env
    ports:
      - "${WHISPER_PORT:-9100}:9000"
    volumes:
      - ./data:/data
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:9000/health"]
      interval: ${HEALTH_CHECK_INTERVAL:-30s}
      timeout: ${HEALTH_CHECK_TIMEOUT:-10s}
      retries: ${HEALTH_CHECK_RETRIES:-5}
    restart: unless-stopped

  # Streamlit Frontend for Job Submission and Monitoring
  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile
    container_name: iav_frontend
    env_file:
      - config.env
    ports:
      - "${STREAMLIT_PORT:-8601}:8501"
    volumes:
      - ./data:/data
    depends_on:
      - redis
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8501/healthz"]
      interval: ${HEALTH_CHECK_INTERVAL:-30s}
      timeout: ${HEALTH_CHECK_TIMEOUT:-10s}
      retries: ${HEALTH_CHECK_RETRIES:-5}
    restart: unless-stopped

  # TikTok Upload Automation Service
  tiktok_uploader:
    build:
      context: ./services
      dockerfile: playwright.dockerfile
    container_name: iav_tiktok_uploader
    env_file:
      - config.env
    ports:
      - "${TIKTOK_UPLOADER_PORT:-8602}:8000"
    volumes:
      - ./data:/data
      - ./services/browser_sessions:/app/browser_sessions
      - ./services/screenshots:/app/screenshots
    depends_on:
      - redis
    environment:
      - DISPLAY=:99
      - PLAYWRIGHT_BROWSERS_PATH=/ms-playwright
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health"]
      interval: ${HEALTH_CHECK_INTERVAL:-30s}
      timeout: ${HEALTH_CHECK_TIMEOUT:-10s}
      retries: ${HEALTH_CHECK_RETRIES:-5}
    restart: unless-stopped

volumes:
  postgres_data:
  redis_data:
  ollama_data:
  n8n_data:
