# TikTok Upload Automation - Playwright Container
FROM mcr.microsoft.com/playwright/python:v1.40.0-jammy

# Set working directory
WORKDIR /app

# Install system dependencies
RUN apt-get update && apt-get install -y \
    curl \
    wget \
    xvfb \
    x11vnc \
    fluxbox \
    && rm -rf /var/lib/apt/lists/*

# Copy requirements first for better caching
COPY requirements.txt .

# Install Python dependencies
RUN pip install --no-cache-dir -r requirements.txt

# Install Playwright browsers
RUN playwright install chromium
RUN playwright install-deps chromium

# Copy application code
COPY . .

# Create necessary directories
RUN mkdir -p /app/browser_sessions /app/screenshots /app/logs

# Set permissions
RUN chmod +x /app/*.py

# Expose port for HTTP API
EXPOSE 8000

# Health check script
COPY healthcheck.py /app/healthcheck.py
RUN chmod +x /app/healthcheck.py

# Start command
CMD ["python", "tiktok_automation_server.py"]
