#!/usr/bin/env python3
"""
Playwright Base Class - Reusable browser automation foundation
Provides anti-detection, session management, and error handling for social media automation
"""

import asyncio
import json
import random
import time
from pathlib import Path
from typing import Optional, Dict, Any
from datetime import datetime

from playwright.async_api import async_playwright, <PERSON><PERSON><PERSON>, BrowserContext, Page
from fake_useragent import UserAgent


class PlaywrightBase:
    """Base class for browser automation with anti-detection measures."""
    
    def __init__(self, platform: str, headless: bool = True, debug: bool = False):
        self.platform = platform
        self.headless = headless
        self.debug = debug
        self.browser: Optional[Browser] = None
        self.context: Optional[BrowserContext] = None
        self.page: Optional[Page] = None
        
        # Paths
        self.sessions_dir = Path("/app/browser_sessions")
        self.screenshots_dir = Path("/app/screenshots")
        self.logs_dir = Path("/app/logs")
        
        # Create directories
        self.sessions_dir.mkdir(exist_ok=True)
        self.screenshots_dir.mkdir(exist_ok=True)
        self.logs_dir.mkdir(exist_ok=True)
        
        # User agent rotation
        self.ua = UserAgent()
        
    async def start_browser(self) -> None:
        """Start browser with anti-detection measures."""
        self.playwright = await async_playwright().start()
        
        # Browser launch options with anti-detection
        launch_options = {
            "headless": self.headless,
            "args": [
                "--no-sandbox",
                "--disable-blink-features=AutomationControlled",
                "--disable-features=VizDisplayCompositor",
                "--disable-extensions",
                "--disable-plugins",
                "--disable-images",  # Faster loading
                "--disable-javascript-harmony-shipping",
                "--disable-background-timer-throttling",
                "--disable-renderer-backgrounding",
                "--disable-backgrounding-occluded-windows",
                "--disable-ipc-flooding-protection",
                "--window-size=1920,1080",
            ]
        }
        
        if not self.headless:
            launch_options["args"].extend([
                "--start-maximized",
                "--disable-web-security",
                "--allow-running-insecure-content"
            ])
        
        self.browser = await self.playwright.chromium.launch(**launch_options)
        
        # Create context with realistic settings
        context_options = {
            "viewport": {"width": 1920, "height": 1080},
            "user_agent": self.ua.random,
            "locale": "en-US",
            "timezone_id": "America/New_York",
            "permissions": ["notifications"],
            "extra_http_headers": {
                "Accept-Language": "en-US,en;q=0.9",
                "Accept-Encoding": "gzip, deflate, br",
                "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8",
                "Connection": "keep-alive",
                "Upgrade-Insecure-Requests": "1",
            }
        }
        
        # Load saved session if exists
        session_file = self.sessions_dir / f"{self.platform}_session.json"
        if session_file.exists():
            try:
                with open(session_file, 'r') as f:
                    session_data = json.load(f)
                context_options["storage_state"] = session_data
                self.log(f"Loaded saved session for {self.platform}")
            except Exception as e:
                self.log(f"Failed to load session: {e}")
        
        self.context = await self.browser.new_context(**context_options)
        
        # Add stealth scripts
        await self.context.add_init_script("""
            // Remove webdriver property
            Object.defineProperty(navigator, 'webdriver', {
                get: () => undefined,
            });
            
            // Mock chrome runtime
            window.chrome = {
                runtime: {},
            };
            
            // Mock permissions
            const originalQuery = window.navigator.permissions.query;
            window.navigator.permissions.query = (parameters) => (
                parameters.name === 'notifications' ?
                    Promise.resolve({ state: Notification.permission }) :
                    originalQuery(parameters)
            );
        """)
        
        self.page = await self.context.new_page()
        
        # Set realistic navigation patterns
        await self.page.set_extra_http_headers({
            "sec-ch-ua": '"Chromium";v="118", "Google Chrome";v="118", "Not=A?Brand";v="99"',
            "sec-ch-ua-mobile": "?0",
            "sec-ch-ua-platform": '"Windows"',
            "sec-fetch-dest": "document",
            "sec-fetch-mode": "navigate",
            "sec-fetch-site": "none",
            "sec-fetch-user": "?1",
        })
        
        self.log(f"Browser started for {self.platform}")
    
    async def save_session(self) -> None:
        """Save current browser session."""
        if not self.context:
            return
            
        try:
            session_data = await self.context.storage_state()
            session_file = self.sessions_dir / f"{self.platform}_session.json"
            
            with open(session_file, 'w') as f:
                json.dump(session_data, f, indent=2)
            
            self.log(f"Session saved for {self.platform}")
        except Exception as e:
            self.log(f"Failed to save session: {e}")
    
    async def screenshot(self, name: str = None) -> str:
        """Take screenshot for debugging."""
        if not self.page:
            return ""
            
        if not name:
            name = f"{self.platform}_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        
        screenshot_path = self.screenshots_dir / f"{name}.png"
        
        try:
            await self.page.screenshot(path=str(screenshot_path), full_page=True)
            self.log(f"Screenshot saved: {screenshot_path}")
            return str(screenshot_path)
        except Exception as e:
            self.log(f"Failed to take screenshot: {e}")
            return ""
    
    async def human_delay(self, min_seconds: float = 1.0, max_seconds: float = 3.0) -> None:
        """Add human-like delay."""
        delay = random.uniform(min_seconds, max_seconds)
        await asyncio.sleep(delay)
    
    async def human_type(self, selector: str, text: str, delay_range: tuple = (0.05, 0.15)) -> None:
        """Type text with human-like delays."""
        if not self.page:
            return
            
        element = await self.page.wait_for_selector(selector, timeout=10000)
        await element.click()
        await self.human_delay(0.5, 1.0)
        
        # Clear existing text
        await element.fill("")
        await self.human_delay(0.2, 0.5)
        
        # Type character by character
        for char in text:
            await element.type(char, delay=random.uniform(*delay_range) * 1000)
    
    async def human_click(self, selector: str, delay_before: tuple = (0.5, 1.5), delay_after: tuple = (1.0, 2.0)) -> None:
        """Click with human-like behavior."""
        if not self.page:
            return
            
        await self.human_delay(*delay_before)
        
        element = await self.page.wait_for_selector(selector, timeout=10000)
        
        # Get element bounds for realistic click position
        box = await element.bounding_box()
        if box:
            # Click at random position within element
            x = box["x"] + random.uniform(0.2, 0.8) * box["width"]
            y = box["y"] + random.uniform(0.2, 0.8) * box["height"]
            await self.page.mouse.click(x, y)
        else:
            await element.click()
        
        await self.human_delay(*delay_after)
    
    async def wait_for_navigation(self, timeout: int = 30000) -> None:
        """Wait for page navigation with timeout."""
        try:
            await self.page.wait_for_load_state("networkidle", timeout=timeout)
        except Exception as e:
            self.log(f"Navigation timeout: {e}")
    
    async def close(self) -> None:
        """Clean up browser resources."""
        try:
            await self.save_session()
            
            if self.page:
                await self.page.close()
            if self.context:
                await self.context.close()
            if self.browser:
                await self.browser.close()
            if hasattr(self, 'playwright'):
                await self.playwright.stop()
                
            self.log(f"Browser closed for {self.platform}")
        except Exception as e:
            self.log(f"Error closing browser: {e}")
    
    def log(self, message: str) -> None:
        """Log message with timestamp."""
        timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        log_message = f"[{timestamp}] [{self.platform.upper()}] {message}"
        
        print(log_message)
        
        # Also write to log file
        log_file = self.logs_dir / f"{self.platform}_automation.log"
        with open(log_file, 'a', encoding='utf-8') as f:
            f.write(log_message + "\n")
